import os
import sys
from glob import glob

import math
import numpy as np
import torch
from sklearn.model_selection import train_test_split
from torch import nn
from tqdm import tqdm
import random

import torch.nn.functional as F
from torch.utils.data import random_split
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix
import seaborn as sns
import torch.distributed as dist

from colorama import Fore,Style

from datasets.RD_dataset_2d import RD_dataset_2d
from datasets.RD_dataset_3d import RD_dataset_3d
from datasets.RD_dataset_4d import RD_dataset_4d
from datasets.RD_dataset_2_5d import RD_dataset_2_5d
from datasets.RD_dataset_5d import RD_dataset_5d

from models.backbone import create_backbone
from models.resnet_3d import create_resnet_3d
from models.backbone_lstm import create_backbone_lstm
from models.backbone_transformer import create_backbone_transformer
from models.backbone_transformer_4d import create_backbone_transformer_4d
from models.backbone_lstm_2_5d import create_backbone_lstm_2_5d
from models.range_sequence_transformer import (
    create_range_sequence_transformer,
    create_range_sequence_transformer_tiny,
    create_range_sequence_transformer_small,
    create_range_sequence_transformer_base,
    create_range_sequence_transformer_large
)
from models.rd_point_fusion_5d import create_rd_point_fusion_5d

from models.classfier_real import classifier_real
from models.classifer_complex import classifier_complex

from models.pretrain.projection_complex import projection_complex
from models.pretrain.projection_real import projection_real
from models.pretrain.predict_complex import predict_complex
from models.pretrain.predict_real import predict_real
from models.pretrain.AdvAugment import Data_augment

from models.pretrain.mae.models_mae import create_mae_vit_base_patch16, create_mae_vit_large_patch16, create_mae_vit_huge_patch14
from models.pretrain.mae.models_vit import create_vit_base_patch16, create_vit_large_patch16, create_vit_huge_patch14, create_vit_tiny_patch16, \
                                            create_vit_tiny_patch16
def set_seed(seed):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def create_model(model_name,**kwargs):
    if model_name == 'backbone':
        return create_backbone(**kwargs)

    elif model_name == 'resnet_3d':
        return create_resnet_3d(**kwargs)

    elif model_name == 'backbone_lstm':
        return create_backbone_lstm(**kwargs)

    elif model_name == 'backbone_transformer':
        return create_backbone_transformer(**kwargs)

    elif model_name == 'backbone_transformer_4d':
        return create_backbone_transformer_4d(**kwargs)

    elif model_name == 'backbone_lstm_2_5d':
        return create_backbone_lstm_2_5d(**kwargs)

    elif model_name == 'rd_point_fusion_5d':
        return create_rd_point_fusion_5d(**kwargs)

    # Range Sequence Transformer
    # -------------------------------------------
    elif model_name == 'range_sequence_transformer':
        return create_range_sequence_transformer(**kwargs)
    elif model_name == 'range_sequence_transformer_tiny':
        return create_range_sequence_transformer_tiny(**kwargs)
    elif model_name == 'range_sequence_transformer_small':
        return create_range_sequence_transformer_small(**kwargs)
    elif model_name == 'range_sequence_transformer_base':
        return create_range_sequence_transformer_base(**kwargs)
    elif model_name == 'range_sequence_transformer_large':
        return create_range_sequence_transformer_large(**kwargs)
    # -------------------------------------------

    # MAE
    # -------------------------------------------
    elif model_name == 'mae_vit_base_patch16':
        return create_mae_vit_base_patch16(**kwargs)
    elif model_name == 'mae_vit_large_patch16':
        return create_mae_vit_large_patch16(**kwargs)
    elif model_name == 'mae_vit_huge_patch14':
        return create_mae_vit_huge_patch14(**kwargs)
    # -------------------------------------------

    # ViT
    # -------------------------------------------
    elif model_name == 'vit_base_patch16':
        return create_vit_base_patch16(**kwargs)
    elif model_name == 'vit_large_patch16':
        return create_vit_large_patch16(**kwargs)
    elif model_name == 'vit_huge_patch14':
        return create_vit_huge_patch14(**kwargs)
    elif model_name == 'vit_tiny_patch16':
        return create_vit_tiny_patch16(**kwargs)
    # -------------------------------------------

    else:
        raise ValueError("Unsupported models name.")

def create_projection(model_type, in_channels=1024, mlp_hidden_size=512, projection_size=128):
    if model_type == 'complex':
        return projection_complex(in_channels=in_channels, mlp_hidden_size=mlp_hidden_size, projection_size=projection_size)
    elif model_type == 'real':
        return projection_real(in_channels=in_channels, mlp_hidden_size=mlp_hidden_size, projection_size=projection_size)

def create_prediction(model_type, in_channels=128, mlp_hidden_size=512, projection_size=128):
    if model_type == 'complex':
        return predict_complex(in_channels=in_channels, mlp_hidden_size=mlp_hidden_size, projection_size=projection_size)
    elif model_type == 'real':
        return predict_real(in_channels=in_channels, mlp_hidden_size=mlp_hidden_size, projection_size=projection_size)


def create_classifier(in_features=1024, num_classes=11, model_type='real'):
    if model_type == 'complex':
        return classifier_complex(in_features, num_classes)
    elif model_type == 'real':
        return classifier_real(in_features, num_classes)

def create_datasets(datasets_name, root=None, k_fold=None, **kwargs):
    # check ddp
    is_ddp = dist.is_initialized()
    is_main_process = not is_ddp or dist.get_rank() == 0

    if k_fold is None:
        k_fold = [0, 1, 2, 3, 4]
    is_random = False
    Dataset = None
    dataset_type = datasets_name.split('_')[2]

    if datasets_name.endswith('_random'):
        is_random = True

    if dataset_type == '2d':
        Dataset = RD_dataset_2d
        # root = "F:\\挑战杯\\RD-zcy"
        if root is None:
            root = r"F:\挑战杯\334_ac_npy_0.3_nomtd\334_ac_all_npy_0.3_nomtd"
        print(f'root:{root}')
    elif dataset_type == '3d':
        Dataset = RD_dataset_3d
        # root = "F:\\挑战杯\\RD_NEW"
        if root is None:
            root = r"F:\挑战杯\334_ac_npy_0.3_nomtd\334_ac_all_npy_0.3_nomtd"
        print(f'root:{root}')
    elif dataset_type == '4d':
        Dataset = RD_dataset_4d
        if root is None:
            root = r"F:\挑战杯\334_ac_npy_0.3_nomtd\334_ac_single_npy_0.3_nomtd"
        if is_main_process:
            print(f'root:{root}')
    elif dataset_type == '2.5d':  # 2.5d
        Dataset = RD_dataset_2_5d
        if root is None:
            root = r"F:\挑战杯\334_ac_npy_0.3_nomtd\334_ac_single_npy_0.3_nomtd"
        print(f'root:{root}')
    elif dataset_type == '5d':
        Dataset = RD_dataset_5d
        if root is None:
            root = r"F:\挑战杯\334_ac_npy_0.3_nomtd\334_ac_all_npy_0.3_nomtd"
        print(f'root:{root}')
    else:
        raise ValueError("Unsupported datasets name.")

    # random_splite       RD_dataset_2d_random, RD_dataset_3d_random
    if is_random:

        all_folders = [os.path.join(root, f) for f in sorted(os.listdir(root))]

        train_folders, test_folders = train_test_split(all_folders, test_size=0.2, random_state=42)
        print("train_folders:", len(train_folders))
        print("test_folders:", len(test_folders))

        train_set = Dataset(train_folders, **kwargs)
        test_set = Dataset(test_folders, **kwargs)

        return list(tuple(train_set, test_set, 0))

    # k_fold continuous split     RD_dataset_2d, RD_dataset_3d
    else:
        k = 5
        folders_k_group=[]
        folders_by_class = {}

        for folder in sorted(os.listdir(root)):
            class_id = int(folder.split('_')[-1])
            if class_id > 4:
                continue
            if class_id not in folders_by_class:
                folders_by_class[class_id] = []
            folders_by_class[class_id].append(os.path.join(root, folder))
        
        # by number
        def extract_number(folder_path):
            folder_name = os.path.basename(folder_path)
            number = int(folder_name.split('_')[0])
            return number
        for _ , folders in folders_by_class.items():
            folders.sort(key=extract_number)

        for fold in range(k):
            if fold not in k_fold:
                continue

            train_folders = []
            test_folders = []

            for class_id, folders in folders_by_class.items():
                
                n = len(folders)
                fold_size = n // k

                idx = np.arange(n)
                start = fold * fold_size
                end = (fold + 1) * fold_size if fold < k - 1 else n
        
                test_idx  = idx[start:end]
                train_idx = np.concatenate([idx[:start], idx[end:]])

                test_folders.extend(folders[i] for i in test_idx)
                train_folders.extend(folders[i] for i in train_idx)
            
            if is_main_process:
                print(f'Fold {fold}: train_folders={len(train_folders)}, test_folders={len(test_folders)}')
            train_set = Dataset(train_folders, **kwargs)
            test_set  = Dataset(test_folders,  **kwargs)
            folders_k_group.append((train_set, test_set, fold))

        return folders_k_group



def create_confusion_matrix(epoch,all_labels, all_preds, num_classes, fold, root_type, dataset_type):
    cm = confusion_matrix(all_labels, all_preds, labels=range(0,num_classes))
    plt.figure(figsize=(6, 4))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', cbar=False,
                xticklabels=[f'Pred {i}' for i in range(num_classes)],
                yticklabels=[f'True {i}' for i in range(num_classes)])

    plt.xlabel('Predicted Label')
    plt.ylabel('True Label')
    # plt.title(f'epoch{epoch}')
    # plt.show()
    
    plt.savefig(f'confusion_matrix_{root_type}_{dataset_type}_{fold}.png')
    plt.close()
# ----------------------------------------------------------------------------
# lr scheduler
def adjust_learning_rate(optimizer, epoch, args):
    """Decay the learning rate with half-cycle cosine after warmup"""
    if epoch < args.warmup_epochs:
        lr = args.lr * epoch / args.warmup_epochs
    else:
        lr = args.min_lr + (args.lr - args.min_lr) * 0.5 * \
            (1. + math.cos(math.pi * (epoch - args.warmup_epochs) / (args.epochs - args.warmup_epochs)))
    for param_group in optimizer.param_groups:
        if "lr_scale" in param_group:
            param_group["lr"] = lr * param_group["lr_scale"]
        else:
            param_group["lr"] = lr
    return lr

#lr decay
def param_groups_lrd(model, weight_decay=0.05, no_weight_decay_list=[], layer_decay=.75):
    """
    Parameter groups for layer-wise lr decay
    """
    param_group_names = {}
    param_groups = {}

    num_layers = len(model.blocks) + 1

    layer_scales = list(layer_decay ** (num_layers - i) for i in range(num_layers + 1))

    for n, p in model.named_parameters():
        if not p.requires_grad:
            continue

        # no decay: all 1D parameters and model specific ones
        if p.ndim == 1 or n in no_weight_decay_list:
            g_decay = "no_decay"
            this_decay = 0.
        else:
            g_decay = "decay"
            this_decay = weight_decay

        layer_id = get_layer_id_for_vit(n, num_layers)
        group_name = "layer_%d_%s" % (layer_id, g_decay)

        if group_name not in param_group_names:
            this_scale = layer_scales[layer_id]

            param_group_names[group_name] = {
                "lr_scale": this_scale,
                "weight_decay": this_decay,
                "params": [],
            }
            param_groups[group_name] = {
                "lr_scale": this_scale,
                "weight_decay": this_decay,
                "params": [],
            }

        param_group_names[group_name]["params"].append(n)
        param_groups[group_name]["params"].append(p)

    # print("parameter groups: \n%s" % json.dumps(param_group_names, indent=2))

    return list(param_groups.values())

def get_layer_id_for_vit(name, num_layers):
    """
    Assign a parameter with its layer id
    """
    if name in ['cls_token', 'pos_embed']:
        return 0
    elif name.startswith('patch_embed'):
        return 0
    elif name.startswith('blocks'):
        return int(name.split('.')[1]) + 1
    else:
        return num_layers
# ----------------------------------------------------------------------------

class Mixup_Criterion(nn.Module):
    def __init__(self, beta, cls_criterion):
        super().__init__()
        self.beta = beta
        self.cls_criterion =  cls_criterion

    def get_mixup_data(self, image, target) :
        beta = np.random.beta(self.beta, self.beta)
        index = torch.randperm(image.size()[0]).to(image.device)
        shuffled_image, shuffled_target = image[index], target[index]
        mixed_image = beta * image + (1 - beta) * shuffled_image
        return mixed_image, shuffled_target, beta

    def forward(self, image, target, net):
        mixed_image, shuffled_target, beta = self.get_mixup_data(image, target)
        pred_mixed = net(mixed_image)
        loss_mixup = beta * self.cls_criterion(pred_mixed, target) + (1 - beta) * self.cls_criterion(pred_mixed, shuffled_target)
        return loss_mixup

def train_one_epoch_2d(model, classifier, optimizer, data_loader, device, epoch, scheduler, warmup=False):
    model.train()
    classifier.train()
    loss_function = torch.nn.CrossEntropyLoss()
    accu_loss = torch.zeros(1).to(device)
    accu_num = torch.zeros(1).to(device)
    optimizer.zero_grad()

    # mixup
    # mixup_criterion = Mixup_Criterion(beta=10, cls_criterion=torch.nn.CrossEntropyLoss())

    sample_num = 0
    data_loader = tqdm(data_loader, file=sys.stdout)
    for step, data in enumerate(data_loader):

        assert len(data) == 3, "data length should be 3"
        images, labels, video_ids = data

        images, labels = images.to(device), labels.to(device)

        sample_num += images.shape[0]

        # #mixup
        # # mixup_criterion.get_mixup_data(images, labels)
        # loss_mixup = mixup_criterion(images, labels, nn.Sequential(model,classfier))

        logits = model(images)
        pred = classifier(logits)
        pred_classes = torch.max(pred, dim=1)[1]
        accu_num += torch.eq(pred_classes, labels.to(device)).sum()

        loss = loss_function(pred, labels)
        # loss = loss_mixup

        optimizer.zero_grad()
        loss.backward()
        accu_loss += loss.detach()

        #sam
        # optimizer.first_step(zero_grad=True)
        # pred = classfier(model(images))
        # loss = loss_function(pred, labels)
        # loss_mixup = mixup_criterion(images, labels, nn.Sequential(model, classfier))
        # loss = loss_mixup
        # loss.backward()
        # optimizer.second_step(zero_grad=True)

        optimizer.step()

        data_loader.desc = Style.RESET_ALL+"[train epoch {}] loss: {:.3f}, acc: {:.2f}".format(epoch,
                                                                               accu_loss.item() / (step + 1),
                                                                               100*accu_num.item() / sample_num)
        if warmup:
            scheduler.step()
    if not warmup:
        scheduler.step()

    return accu_loss.item() / len(data_loader), accu_num.item() / sample_num

def train_one_epoch_3d(model, classifier, optimizer, data_loader, device, epoch, scheduler, warmup=False):
    model.train()
    classifier.train()
    loss_function = torch.nn.CrossEntropyLoss()
    accu_loss = torch.zeros(1).to(device)
    accu_num = torch.zeros(1).to(device)
    optimizer.zero_grad()

    sample_num = 0
    data_loader = tqdm(data_loader, file=sys.stdout)
    for step, data in enumerate(data_loader):
        videos, lengths, labels = data
        videos, labels = videos.to(device), labels.to(device)
        
        sample_num += sum(lengths)
        
        features = model(videos, lengths)  # [B, T, D]
        
        batch_size, seq_len = features.shape[0], features.shape[1]

        mask = torch.arange(seq_len, device=device).unsqueeze(0) < torch.tensor(lengths, device=device).unsqueeze(1) #[B, T]

        valid_features = features[mask]    # [B*valid_frames, D]
        
        repeated_labels = labels.unsqueeze(1).expand(batch_size, seq_len)
        valid_labels = repeated_labels[mask]  # [B*valid_frames]

        pred = classifier(valid_features)
        pred_classes = torch.max(pred, dim=1)[1]
        
        accu_num += torch.eq(pred_classes, valid_labels).sum()
        
        loss = loss_function(pred, valid_labels)
        
        optimizer.zero_grad()
        loss.backward()
        accu_loss += loss.detach()
        
        optimizer.step()
        
        data_loader.desc = Style.RESET_ALL+"[train epoch {}] loss: {:.3f}, acc: {:.2f}".format(
            epoch,
            accu_loss.item() / (step + 1),
            100*accu_num.item() / sample_num
        )
            
        if warmup:
            scheduler.step()
    if not warmup:
        scheduler.step()

    return accu_loss.item() / len(data_loader), accu_num.item() / sample_num

def train_one_epoch_4d(model, classifier, optimizer, data_loader, device, epoch, scheduler, warmup=False, alpha=1.0, gradient_monitor=None):
    model.train()
    classifier.train()
    loss_function = torch.nn.CrossEntropyLoss()

    accu_loss = torch.zeros(1).to(device) # all
    accu_loss_intermediate = torch.zeros(1).to(device)
    accu_loss_final = torch.zeros(1).to(device)

    accu_num = torch.zeros(1).to(device)
    accu_num_intermediate = torch.zeros(1).to(device)
    optimizer.zero_grad()

    sample_num = 0

    # check ddp
    is_ddp = dist.is_initialized()
    is_main_process = not is_ddp or dist.get_rank() == 0

    if is_main_process:
        data_loader = tqdm(data_loader, file=sys.stdout)
    for step, data in enumerate(data_loader):
        trail_frames, T_lengths, n_lengths, labels = data
        trail_frames, labels = trail_frames.to(device), labels.to(device)

        # T_lengths: [T1, T2, ..., TB]
        sample_num += sum(T_lengths)

        final_features, intermediate_features = model(trail_frames, T_lengths, n_lengths, return_intermediate=True)  # [B, T, D]

        batch_size, seq_len = final_features.shape[0], final_features.shape[1]

        mask = torch.arange(seq_len, device=device).unsqueeze(0) < torch.tensor(T_lengths, device=device).unsqueeze(1) #[B, T]

        # final loss
        valid_final_features = final_features[mask]    # [B*valid_frames, D]
        repeated_labels = labels.unsqueeze(1).expand(batch_size, seq_len)
        valid_labels = repeated_labels[mask]  # [B*valid_frames]

        pred_final = classifier(valid_final_features)
        pred_final_classes = torch.max(pred_final, dim=1)[1]
        accu_num += torch.eq(pred_final_classes, valid_labels).sum()
        loss_final = loss_function(pred_final, valid_labels)

        # intermediate loss
        valid_intermediate_features = intermediate_features[mask]    # [B*valid_frames, D]
        pred_intermediate = classifier(valid_intermediate_features)
        pred_intermediate_classes = torch.max(pred_intermediate, dim=1)[1]
        accu_num_intermediate += torch.eq(pred_intermediate_classes, valid_labels).sum()
        loss_intermediate = loss_function(pred_intermediate, valid_labels)

        # Combined loss
        loss = loss_final + alpha * loss_intermediate

        optimizer.zero_grad()
        loss.backward()

        if gradient_monitor is not None and is_main_process:
            gradient_monitor.record_gradients(epoch)

        accu_loss += loss.detach()
        accu_loss_final += loss_final.detach()
        accu_loss_intermediate += loss_intermediate.detach()

        optimizer.step()

        if is_main_process and hasattr(data_loader, 'desc'):
            data_loader.desc = Style.RESET_ALL+"[train epoch {}] loss: {:.3f} (final: {:.3f}, inter: {:.3f}), final: {:.2f}, inter: {:.2f}".format(
                epoch,
                accu_loss.item() / (step + 1),
                accu_loss_final.item() / (step + 1),
                accu_loss_intermediate.item() / (step + 1),
                100*accu_num.item() / sample_num,
                100*accu_num_intermediate.item() / sample_num
            )

        if warmup:
            scheduler.step()
    if not warmup:
        scheduler.step()

    if is_ddp:
        dist.all_reduce(accu_loss, op=dist.ReduceOp.SUM)
        dist.all_reduce(accu_num, op=dist.ReduceOp.SUM)

        sample_num_tensor = torch.tensor(sample_num, dtype=torch.float, device=device)
        dist.all_reduce(sample_num_tensor, op=dist.ReduceOp.SUM)
        sample_num = sample_num_tensor.item()

        loader_len_tensor = torch.tensor(len(data_loader), dtype=torch.float, device=device)
        dist.all_reduce(loader_len_tensor, op=dist.ReduceOp.SUM)
        loader_len = loader_len_tensor.item()
    else:
        loader_len = len(data_loader)

    return accu_loss.item() / loader_len, accu_num.item() / sample_num

def train_one_epoch_2_5d(model, classifier, optimizer, data_loader, device, epoch, scheduler, warmup=False):
    model.train()
    classifier.train()
    loss_function = torch.nn.CrossEntropyLoss()
    accu_loss = torch.zeros(1).to(device)
    accu_num = torch.zeros(1).to(device)
    optimizer.zero_grad()

    sample_num = 0
    data_loader = tqdm(data_loader, file=sys.stdout)
    for step, data in enumerate(data_loader):
        frames, lengths, labels, video_ids = data
        frames, labels = frames.to(device), labels.to(device)

        sample_num += frames.shape[0]

        point_features = model(frames, lengths)  # [B, features_dim]

        pred = classifier(point_features)
        pred_classes = torch.max(pred, dim=1)[1]
        accu_num += torch.eq(pred_classes, labels).sum()

        loss = loss_function(pred, labels)

        optimizer.zero_grad()
        loss.backward()
        accu_loss += loss.detach()

        optimizer.step()

        data_loader.desc = Style.RESET_ALL+"[train epoch {}] loss: {:.3f}, acc: {:.2f}".format(
            epoch,
            accu_loss.item() / (step + 1),
            100*accu_num.item() / sample_num
        )

        if warmup:
            scheduler.step()
    if not warmup:
        scheduler.step()

    return accu_loss.item() / len(data_loader), accu_num.item() / sample_num

@torch.no_grad()
def evaluate_2_5d(model, classifier, data_loader, device, epoch):
    model.eval()
    classifier.eval()

    accu_num = torch.zeros(1).to(device)
    sample_num = 0

    all_labels = []
    all_preds = []

    video_correct = 0
    total_videos = 0
    video_data = {}

    data_loader = tqdm(data_loader, file=sys.stdout)
    for step, data in enumerate(data_loader):
        frames, lengths, labels, video_ids = data
        frames, labels = frames.to(device), labels.to(device)

        sample_num += frames.shape[0]

        point_features = model(frames, lengths)  # [B, features_dim]

        pred = F.softmax(classifier(point_features), dim=1)
        pred_classes = torch.max(pred, dim=1)[1]
        accu_num += torch.eq(pred_classes, labels).sum()

        all_labels.extend(labels.cpu().numpy().tolist())
        all_preds.extend(pred_classes.cpu().numpy().tolist())

        for i, video_id in enumerate(video_ids):
            if video_id not in video_data:
                video_data[video_id] = {'preds': [], 'labels': []}
            video_data[video_id]['preds'].append(pred_classes[i].cpu().item())
            video_data[video_id]['labels'].append(labels[i].cpu().item())

        current_video_correct = 0
        current_total_videos = len(video_data)
        for video_id, data_dict in video_data.items():
            preds = data_dict['preds']
            labels = data_dict['labels']
            correct_frames = sum(1 for p, l in zip(preds, labels) if p == l)
            total_frames = len(preds)
            if correct_frames / total_frames >= 0.9:
                current_video_correct += 1

        current_video_acc = current_video_correct / current_total_videos if current_total_videos > 0 else 0.0

        data_loader.desc = Fore.RED+"[valid epoch {}] point_acc: {:.2f}, video_acc: {:.2f}".format(
            epoch,
            100*accu_num.item() / sample_num,
            100*current_video_acc
        )

    total_videos = len(video_data)
    for video_id, data_dict in video_data.items():
        preds = data_dict['preds']
        labels = data_dict['labels']

        correct_frames = sum(1 for p, l in zip(preds, labels) if p == l)
        total_frames = len(preds)

        if correct_frames / total_frames >= 0.9:
            video_correct += 1

    video_acc = video_correct / total_videos if total_videos > 0 else 0.0
    point_acc = accu_num.item() / sample_num

    return (point_acc, video_acc), all_labels, all_preds

@torch.no_grad()
def evaluate_2d(model, classifier, data_loader, device, epoch):

    model.eval()
    classifier.eval()

    accu_num = torch.zeros(1).to(device)
    sample_num = 0

    all_labels = []
    all_preds = []

    video_correct = 0
    total_videos = 0
    video_data = {}

    data_loader = tqdm(data_loader, file=sys.stdout)
    for step, data in enumerate(data_loader):
        assert len(data) == 3, "data length should be 3"
        images, labels, video_ids = data

        images = images.to(device)
        labels = labels.to(device)

        sample_num += images.shape[0]

        pred = F.softmax(classifier(model(images.to(device))),dim=1)

        pred_classes = torch.max(pred, dim=1)[1]
        accu_num += torch.eq(pred_classes, labels.to(device)).sum()

        all_labels.extend(labels.cpu().numpy().tolist())
        all_preds.extend(pred_classes.cpu().numpy().tolist())

        for i, video_id in enumerate(video_ids):
            if video_id not in video_data:
                video_data[video_id] = {'preds': [], 'labels': []}
            video_data[video_id]['preds'].append(pred_classes[i].cpu().item())
            video_data[video_id]['labels'].append(labels[i].cpu().item())

        current_video_correct = 0
        current_total_videos = len(video_data)
        for video_id, data_dict in video_data.items():
            preds = data_dict['preds']
            labels = data_dict['labels']
            correct_frames = sum(1 for p, l in zip(preds, labels) if p == l)
            total_frames = len(preds)
            if correct_frames / total_frames >= 0.9:
                current_video_correct += 1

        current_video_acc = current_video_correct / current_total_videos if current_total_videos > 0 else 0.0

        data_loader.desc = Fore.RED+"[valid epoch {}] frame_acc: {:.2f}, video_acc: {:.2f}".format(
            epoch,
            100*accu_num.item() / sample_num,
            100*current_video_acc
        )

    total_videos = len(video_data)
    for video_id, data_dict in video_data.items():
        preds = data_dict['preds']
        labels = data_dict['labels']

        correct_frames = sum(1 for p, l in zip(preds, labels) if p == l)
        total_frames = len(preds)

        if correct_frames / total_frames >= 0.9:
            video_correct += 1

    video_acc = video_correct / total_videos if total_videos > 0 else 0.0
    frame_acc = accu_num.item() / sample_num

    return (frame_acc, video_acc), all_labels, all_preds

@torch.no_grad()
def evaluate_3d(model, classifier, data_loader, device, epoch):
    model.eval()
    classifier.eval()

    accu_num = torch.zeros(1).to(device)
    total_frames = 0

    all_labels = []
    all_preds = []
    
    video_correct = 0
    total_videos = 0

    data_loader = tqdm(data_loader, file=sys.stdout)
    for step, data in enumerate(data_loader):
        videos, lengths, labels = data
        videos, labels = videos.to(device), labels.to(device)
        
        features = model(videos, lengths)  # [B, T, D]
        
        batch_size, seq_len = features.shape[0], features.shape[1]
        
        mask = torch.arange(seq_len, device=device).unsqueeze(0) < torch.tensor(lengths, device=device).unsqueeze(1) #[B, T]
        
        valid_features = features[mask]  # [B*valid_frames, D]
        
        repeated_labels = labels.unsqueeze(1).expand(batch_size, seq_len)
        valid_labels = repeated_labels[mask]  # [B*valid_frames]
        
        pred = F.softmax(classifier(valid_features), dim=1)
        pred_classes = torch.max(pred, dim=1)[1]
        
        accu_num += torch.eq(pred_classes, valid_labels).sum()
        total_frames += sum(lengths)
        
        all_labels.extend(valid_labels.cpu().numpy().tolist())
        all_preds.extend(pred_classes.cpu().numpy().tolist())
        
        total_videos += batch_size
        
        # video-level
        start_idx = 0
        for i, length in enumerate(lengths):
            
            end_idx = start_idx + length
            video_preds = pred_classes[start_idx:end_idx]
            video_labels = valid_labels[start_idx:end_idx]
            
            correct_frames = torch.eq(video_preds, video_labels).sum().item()
            
            if correct_frames / length >= 0.9:
                video_correct += 1
                
            start_idx = end_idx

        data_loader.desc = Fore.RED+"[valid epoch {}] frame_acc: {:.2f}, video_acc: {:.2f}".format(epoch, 
                                                                                                    100*accu_num.item() / total_frames,
                                                                                                    100*video_correct / total_videos)

    return (accu_num.item() / total_frames, video_correct / total_videos), all_labels, all_preds

@torch.no_grad()
def evaluate_3d_test(model, classifier, data_loader, device, epoch):

    model.eval()
    classifier.eval()

    accu_num = torch.zeros(1).to(device)
    total_frames = 0

    all_labels = []
    all_preds = []

    video_correct = 0
    total_videos = 0

    data_loader = tqdm(data_loader, file=sys.stdout)
    for data in data_loader:
        videos, lengths, labels = data
        videos, labels = videos.to(device), labels.to(device)

        assert videos.shape[0] == 1, "Batch size should be 1"

        video = videos[0]
        video = video.permute(1, 0, 2, 3) # [T, C, H, W]
        length = lengths[0]
        label = labels[0]

        total_videos += 1
        total_frames += length

        frame_preds = []
        frame_labels = []

        for frame_idx in range(length):
            current_frames = video[:frame_idx+1].unsqueeze(0)
            current_length = torch.tensor([frame_idx+1], device=device)

            current_frames = current_frames.permute(0, 2, 1, 3, 4) # [B, C, T, H, W]
            features = model(current_frames, current_length)

            current_feature = features[0, -1:, :]

            pred = F.softmax(classifier(current_feature), dim=1)
            pred_class = torch.max(pred, dim=1)[1]

            frame_preds.append(pred_class.item())
            frame_labels.append(label.item())

            accu_num += torch.eq(pred_class, label).sum()

        frame_preds_tensor = torch.tensor(frame_preds, device=device)
        frame_labels_tensor = torch.tensor(frame_labels, device=device)

        correct_frames = torch.eq(frame_preds_tensor, frame_labels_tensor).sum().item()
        if correct_frames / length >= 0.9:
            video_correct += 1

        all_labels.extend(frame_labels)
        all_preds.extend(frame_preds)

        data_loader.desc = Fore.BLUE+"[test epoch {}] frame_acc: {:.2f}, video_acc: {:.2f}".format(epoch,
                                                                                                   100*accu_num.item() / total_frames,
                                                                                                   100*video_correct / total_videos)

    return (accu_num.item() / total_frames, video_correct / total_videos), all_labels, all_preds

@torch.no_grad()
def evaluate_4d_test(model, classifier, data_loader, device, epoch):

    model.eval()
    classifier.eval()

    accu_num = torch.zeros(1).to(device)
    total_frames = 0

    all_labels = []
    all_preds = []

    video_correct = 0
    total_videos = 0

    video_scores = []

    data_loader = tqdm(data_loader, file=sys.stdout)
    for data in data_loader:
        trail_frames, T_lengths, n_lengths, labels = data
        trail_frames, labels = trail_frames.to(device), labels.to(device)

        assert trail_frames.shape[0] == 1, "Batch size should be 1 for test mode"

        trail = trail_frames[0]
        T_length = T_lengths[0]
        trail_n_lengths = n_lengths[:T_length]
        label = labels[0]

        total_videos += 1
        total_frames += T_length

        frame_preds = []
        frame_labels = []

        for t_idx in range(T_length):
            current_trail = trail[:t_idx+1].unsqueeze(0)
            current_T_length = [t_idx+1]
            current_n_lengths = trail_n_lengths[:t_idx+1]

            features = model(current_trail, current_T_length, current_n_lengths, return_intermediate=False)

            current_feature = features[0, -1:, :]  # [1, features_dim]

            pred = F.softmax(classifier(current_feature), dim=1)
            pred_class = torch.max(pred, dim=1)[1]

            frame_preds.append(pred_class.item())
            frame_labels.append(label.item())

            accu_num += torch.eq(pred_class, label).sum()

        frame_preds_tensor = torch.tensor(frame_preds, device=device)
        frame_labels_tensor = torch.tensor(frame_labels, device=device)

        correct_frames = torch.eq(frame_preds_tensor, frame_labels_tensor).sum().item()
        video_score = correct_frames / T_length
        video_scores.append(video_score)

        if video_score >= 0.9:
            video_correct += 1

        all_labels.extend(frame_labels)
        all_preds.extend(frame_preds)

        data_loader.desc = Fore.BLUE+"[test epoch {}] frame_acc: {:.2f}, video_acc: {:.2f}, avg_score: {:.2f}".format(
            epoch,
            100*accu_num.item() / total_frames,
            100*video_correct / total_videos,
            100*sum(video_scores) / len(video_scores) if video_scores else 0
        )

    frame_acc = accu_num.item() / total_frames
    video_acc = video_correct / total_videos
    avg_video_score = sum(video_scores) / len(video_scores) if video_scores else 0

    print(f"\n[Test Epoch {epoch}] Results:")
    print(f"Frame Accuracy: {frame_acc*100:.2f}%")
    print(f"Video Accuracy (>=90%): {video_acc*100:.2f}%")
    print(f"Average Video Score: {avg_video_score*100:.2f}%")
    print(f"Evaluated {len(video_scores)} videos")

    return (frame_acc, video_acc, avg_video_score), all_labels, all_preds

@torch.no_grad()
def evaluate_4d(model, classifier, data_loader, device, epoch):
    model.eval()
    classifier.eval()

    accu_num = torch.zeros(1).to(device)
    total_frames = 0

    all_labels = []
    all_preds = []

    video_correct = 0
    total_videos = 0

    # when correct
    video_scores = []

    import torch.distributed as dist
    is_ddp = dist.is_initialized()
    is_main_process = not is_ddp or dist.get_rank() == 0

    if is_main_process:
        data_loader = tqdm(data_loader, file=sys.stdout)
    for step, data in enumerate(data_loader):
        trail_frames, T_lengths, n_lengths, labels = data
        trail_frames, labels = trail_frames.to(device), labels.to(device)

        features = model(trail_frames, T_lengths, n_lengths, return_intermediate=False)  # [B, T, D]

        batch_size, seq_len = features.shape[0], features.shape[1]

        mask = torch.arange(seq_len, device=device).unsqueeze(0) < torch.tensor(T_lengths, device=device).unsqueeze(1) #[B, T]

        valid_features = features[mask]  # [B*valid_frames, D]

        repeated_labels = labels.unsqueeze(1).expand(batch_size, seq_len)
        valid_labels = repeated_labels[mask]  # [B*valid_frames]

        pred = F.softmax(classifier(valid_features), dim=1)
        pred_classes = torch.max(pred, dim=1)[1]

        accu_num += torch.eq(pred_classes, valid_labels).sum()
        total_frames += sum(T_lengths)

        all_labels.extend(valid_labels.cpu().numpy().tolist())
        all_preds.extend(pred_classes.cpu().numpy().tolist())

        total_videos += batch_size

        # video-level
        start_idx = 0
        for i, t_length in enumerate(T_lengths):

            end_idx = start_idx + t_length
            video_preds = pred_classes[start_idx:end_idx]
            video_labels = valid_labels[start_idx:end_idx]

            correct_frames = torch.eq(video_preds, video_labels).sum().item()

            if correct_frames / t_length >= 0.9:
                video_correct += 1

            correct_flag = False
            video_score = 15.0
            for frame_idx in range(min(t_length, 10)):
                if video_preds[frame_idx] == video_labels[frame_idx]:
                    correct_flag = True
                    break
                else:
                    video_score -= 1.5
            if not correct_flag:
                video_score = 0.0
            video_scores.append(video_score)

            start_idx = end_idx

        if is_main_process and hasattr(data_loader, 'desc'):
            data_loader.desc = Fore.RED+"[valid epoch {}] frame_acc: {:.2f}, video_acc: {:.2f}".format(epoch,
                                                                                                        100*accu_num.item() / total_frames,
                                                                                                        100*video_correct / total_videos)

    # ddp
    if is_ddp:
        dist.all_reduce(accu_num, op=dist.ReduceOp.SUM)
        total_frames_tensor = torch.tensor(total_frames, dtype=torch.float, device=device)
        dist.all_reduce(total_frames_tensor, op=dist.ReduceOp.SUM)
        total_frames = int(total_frames_tensor.item())

        video_correct_tensor = torch.tensor(video_correct, dtype=torch.float, device=device)
        total_videos_tensor = torch.tensor(total_videos, dtype=torch.float, device=device)
        dist.all_reduce(video_correct_tensor, op=dist.ReduceOp.SUM)
        dist.all_reduce(total_videos_tensor, op=dist.ReduceOp.SUM)
        video_correct = int(video_correct_tensor.item())
        total_videos = int(total_videos_tensor.item())

        local_data_len = len(all_labels)
        data_len_tensor = torch.tensor(local_data_len, dtype=torch.long, device=device)

        gathered_lens = [torch.zeros_like(data_len_tensor) for _ in range(dist.get_world_size())]
        dist.all_gather(gathered_lens, data_len_tensor)
        max_len = max([lens.item() for lens in gathered_lens])

        if max_len > 0:
            if local_data_len < max_len:
                all_labels.extend([0] * (max_len - local_data_len))
                all_preds.extend([0] * (max_len - local_data_len))

            all_labels_tensor = torch.tensor(all_labels[:max_len], dtype=torch.long, device=device)
            all_preds_tensor = torch.tensor(all_preds[:max_len], dtype=torch.long, device=device)

            gathered_labels = [torch.zeros_like(all_labels_tensor) for _ in range(dist.get_world_size())]
            gathered_preds = [torch.zeros_like(all_preds_tensor) for _ in range(dist.get_world_size())]

            dist.all_gather(gathered_labels, all_labels_tensor)
            dist.all_gather(gathered_preds, all_preds_tensor)

            if is_main_process:
                all_labels = []
                all_preds = []
                for i, (labels_tensor, preds_tensor) in enumerate(zip(gathered_labels, gathered_preds)):
                    actual_len = gathered_lens[i].item()
                    if actual_len > 0:
                        all_labels.extend(labels_tensor[:actual_len].cpu().numpy().tolist())
                        all_preds.extend(preds_tensor[:actual_len].cpu().numpy().tolist())

        local_scores_len = len(video_scores)
        scores_len_tensor = torch.tensor(local_scores_len, dtype=torch.long, device=device)

        gathered_scores_lens = [torch.zeros_like(scores_len_tensor) for _ in range(dist.get_world_size())]
        dist.all_gather(gathered_scores_lens, scores_len_tensor)
        max_scores_len = max([lens.item() for lens in gathered_scores_lens])

        if max_scores_len > 0:
            if local_scores_len < max_scores_len:
                video_scores.extend([0.0] * (max_scores_len - local_scores_len))

            video_scores_tensor = torch.tensor(video_scores[:max_scores_len], dtype=torch.float, device=device)
            gathered_scores = [torch.zeros_like(video_scores_tensor) for _ in range(dist.get_world_size())]
            dist.all_gather(gathered_scores, video_scores_tensor)

            if is_main_process:
                video_scores = []
                for i, scores_tensor in enumerate(gathered_scores):
                    actual_len = gathered_scores_lens[i].item()
                    if actual_len > 0:
                        video_scores.extend(scores_tensor[:actual_len].cpu().numpy().tolist())

    if is_main_process and video_scores:
        average_score = sum(video_scores) / len(video_scores)
        print(f"\n[Epoch {epoch}] video_score: {average_score:.2f}")
        print(f"evaluate {len(video_scores)} videos")
        print(f"video_acc: {video_correct / total_videos:.2f}")
        print(f"frame_acc: {accu_num.item() / total_frames:.2f}")

    return (accu_num.item() / total_frames, video_correct / total_videos), all_labels, all_preds

def pretrain_Supcon_2d(encoder, projection, criterion, optimizer, train_loader, epoch, device, batch_size, max_epoch, scheduler, warm_up=False, decoder=None, img_noise=False, img_crop=False):
    encoder.train()
    projection.train()
    # decoder.train()
    # mse = nn.MSELoss()
    view = 2 if img_crop or img_noise else 1

    AdvAugment = Data_augment(img_noise=img_noise, img_horizontal_flip=False, img_random_mask=False)

    torch.cuda.empty_cache()

    total_loss, total_num, train_bar = 0.0, 0, tqdm(train_loader)

    loss = 0
    for i, data in enumerate(train_bar):
        assert len(data) == 4, "data length should be 4"
        images, images_crop, label, video_id = data

        # images_contrastive = torch.cat([images[0], images[1]], dim=0)
        # image_reconstruct = images[2]
        # images_contrastive, image_reconstruct, label = (images_contrastive.cuda(non_blocking=True),
        #                                                 image_reconstruct.cuda(non_blocking=True),
        #                                                 label.cuda(non_blocking=True))
        images_contrastive_1, images_contrastive_2, label = images.to(device), images_crop.to(device), label.to(device)

        # augment -two view
        if view == 2:
            if not img_crop:
                images_contrastive_2 = images_contrastive_1.clone()
            images_contrastive = torch.cat((AdvAugment(images_contrastive_1), AdvAugment(images_contrastive_2)), dim=0)
        else:
            images_contrastive = images_contrastive_1

        # SupContrast
        features_contrastive = encoder(images_contrastive)
        outs_contrastive = projection(features_contrastive) # [batch*view, feature]

        # if two view
        if view == 2:
            out1, out2 = torch.split(outs_contrastive, [batch_size, batch_size], dim=0)   # [batch, feature] * view
            outs = torch.cat([out1.unsqueeze(1), out2.unsqueeze(1)], dim=1)    # [batch, view, feature]
        else:
        # if only one view
            outs = outs_contrastive.unsqueeze(1)

        loss_contrast = criterion(outs, label)

        # reconstruct
        # feature_reconstruct = encoder(image_reconstruct)
        # reconstruct_image = decoder(feature_reconstruct)
        # loss_re = mse(image_reconstruct, reconstruct_image)

        # loss = options['alpha_supcl'] * loss_contrast + options['alpha_res'] * loss_re\
        loss = loss_contrast
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        total_num += batch_size
        total_loss += loss.item() * batch_size
        loss = total_loss / total_num
        train_bar.set_description('Train Epoch: [{}/{}] Loss: {:.4f}'.format(epoch + 1, max_epoch, loss))
        if warm_up:
            scheduler.step()

    if not warm_up:
        scheduler.step()

    return loss

def pretrain_Supcon_3d(encoder, projection, criterion, optimizer, train_loader, epoch, device, batch_size, max_epoch, scheduler, warm_up=False, img_noise=False, img_crop=False):
    encoder.train()
    projection.train()
    
    AdvAugment = Data_augment(rotate_and_flip=True)
    
    torch.cuda.empty_cache()
    
    total_loss, total_num = 0.0, 0
    train_bar = tqdm(train_loader, file=sys.stdout)
    
    for i, (videos, lengths, labels) in enumerate(train_bar):

        videos_contrastive, labels = videos.to(device), labels.to(device)
        
        # augment
        # videos_contrastive = torch.cat([AdvAugment(videos_contrastive), AdvAugment(videos_contrastive)], dim=0)
        # lengths = torch.cat([lengths, lengths], dim=0)

        features_contrastive = encoder(videos_contrastive, lengths) # [batch*view, T, D]

        seq_len = features_contrastive.shape[1]
        
        mask = torch.arange(seq_len, device=device).unsqueeze(0) < torch.tensor(lengths, device=device).unsqueeze(1)  # [batch*view, T]
        
        valid_contrastive_features = features_contrastive[mask] # [batch*view*valid_frames, D]
        
        repeated_labels = labels.unsqueeze(1).expand(batch_size, seq_len)
        valid_labels = repeated_labels[mask]  # [batch*valid_frames]

        outs_contrastive = projection(valid_contrastive_features) # [batch*view*valid_frames, feature]

        # if two view
        # out1, out2 = torch.split(outs_contrastive, [batch_size, batch_size], dim=0) #[batch*valid_frames, feature] * view
        # outs = torch.cat([out1.unsqueeze(1), out2.unsqueeze(1)], dim=1)   #[batch*valid_frames, view, feature]

        # if only one view
        outs = outs_contrastive.unsqueeze(1)
        
        loss_contrast = criterion(outs, valid_labels)
        
        loss = loss_contrast
        
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        valid_frames_count = sum(lengths)

        total_num += valid_frames_count
        total_loss += loss.item() * valid_frames_count
        current_loss = total_loss / total_num
        
        train_bar.set_description('Train Epoch: [{}/{}] Loss: {:.4f}'.format(epoch + 1, max_epoch, current_loss))
        
        if warm_up:
            scheduler.step()
    
    if not warm_up:
        scheduler.step()
    
    return current_loss

def pretrain_mae(model, train_loader, optimizer, device, epoch, args=None):
    model.train()
    max_epoch = args.epochs if args is not None and hasattr(args, 'epochs') else 100

    total_loss, total_num = 0.0, 0
    train_bar = tqdm(train_loader, file=sys.stdout)
    current_loss = 0.0

    for data_iter_step, (samples, _) in enumerate(train_bar):
        if args is not None and hasattr(args, 'accum_iter'):
            if data_iter_step % args.accum_iter == 0:
                adjust_learning_rate(optimizer, data_iter_step / len(train_loader) + epoch, args)

        samples = samples.to(device, non_blocking=True)
        batch_size = samples.shape[0]

        mask_ratio = args.mask_ratio if args is not None and hasattr(args, 'mask_ratio') else 0.75
        loss, _, _ = model(samples, mask_ratio=mask_ratio)

        loss_value = loss.item()
        if not math.isfinite(loss_value):
            print("Loss is {}, stopping training".format(loss_value))
            sys.exit(1)

        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        total_num += batch_size
        total_loss += loss_value * batch_size
        current_loss = total_loss / total_num
        
        train_bar.set_description('Train Epoch: [{}/{}] Loss: {:.4f}'.format(epoch + 1, max_epoch, current_loss))

    return current_loss


def finetune_mae(model, classifier, criterion, data_loader, optimizer, device, epoch, clip_grad=None, mixup_fn=None, args=None):
    model.train()
    classifier.train()
    loss_function = criterion
    accu_loss = torch.zeros(1).to(device)
    accu_num = torch.zeros(1).to(device)
    optimizer.zero_grad()

    sample_num = 0
    data_loader = tqdm(data_loader, file=sys.stdout)
    for step, (samples, targets) in enumerate(data_loader):
        samples = samples.to(device)
        targets = targets.to(device)
        
        sample_num += samples.shape[0]
        
        # 调整学习率
        if args is not None and hasattr(args, 'accum_iter'):
            if step % args.accum_iter == 0:
                adjust_learning_rate(optimizer, step / len(data_loader) + epoch, args)
        
        if mixup_fn is not None:
            samples, targets = mixup_fn(samples, targets)
            
        outputs = classifier(model(samples))
        loss = loss_function(outputs, targets)
        
        pred_classes = torch.max(outputs, dim=1)[1]
        accu_num += torch.eq(pred_classes, targets).sum()
        
        optimizer.zero_grad()
        loss.backward()
        accu_loss += loss.detach()
        
        if clip_grad is not None:
            torch.nn.utils.clip_grad_norm_(model.parameters(), clip_grad)
            
        optimizer.step()
        
        data_loader.desc = Style.RESET_ALL+"[train epoch {}] loss: {:.3f}, acc: {:.2f}".format(epoch,
                                                                           accu_loss.item() / (step + 1),
                                                                           100*accu_num.item() / sample_num)
        if not torch.isfinite(loss):
            print('WARNING: non-finite loss, ending training ', loss)
            sys.exit(1)
            
    return accu_loss.item() / len(data_loader), accu_num.item() / sample_num * 100




